import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { BlogContext } from '../context/SupabaseBlogContext';
import { useDebounce } from '../hooks/useDebounce';

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number | string;
  disabled?: boolean;
  className?: string;
  autoHeight?: boolean;
  // Enhanced features
  enableAutoSave?: boolean;
  autoSaveDelay?: number;
  onAutoSave?: (content: string) => Promise<void>;
  showWordCount?: boolean;
  showDetailedStats?: boolean;
  enableKeyboardShortcuts?: boolean;
  enableMediaUpload?: boolean;
  enableLinking?: boolean;
  enableTables?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = React.memo(({
  value,
  onChange,
  placeholder = 'Start writing your content...',
  height = 400,
  disabled = false,
  className = '',
  autoHeight = false,
  enableAutoSave = true,
  autoSaveDelay = 30000,
  onAutoSave,
  showWordCount = true,
  showDetailedStats = false,
  enableKeyboardShortcuts = true,
  enableMediaUpload = true,
  enableLinking = true,
  enableTables = true
}) => {
  const { uploadPostImage } = useContext(BlogContext);
  const editorRef = useRef<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);

  // Debounced onChange to prevent excessive parent updates
  const debouncedOnChange = useDebounce(onChange, 300);

  // Auto-save functionality
  const debouncedAutoSave = useDebounce(async (content: string) => {
    if (onAutoSave && enableAutoSave && content.trim()) {
      try {
        await onAutoSave(content);
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }
  }, autoSaveDelay);

  // Calculate reading time (average 200 words per minute)
  const calculateReadingTime = useCallback((text: string) => {
    const words = text.trim().split(/\s+/).length;
    return Math.ceil(words / 200);
  }, []);

  // Update statistics
  const updateStats = useCallback((content: string) => {
    const textContent = content.replace(/<[^>]*>/g, ''); // Strip HTML tags
    const words = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;
    const characters = textContent.length;
    const reading = calculateReadingTime(textContent);

    setWordCount(words);
    setCharacterCount(characters);
    setReadingTime(reading);
  }, [calculateReadingTime]);

  // Handle content change
  const handleEditorChange = useCallback((content: string) => {
    updateStats(content);
    debouncedOnChange(content);
    debouncedAutoSave(content);
  }, [updateStats, debouncedOnChange, debouncedAutoSave]);

  // Handle image upload
  const handleImageUpload = useCallback(async (blobInfo: any, progress: (percent: number) => void): Promise<string> => {
    if (!enableMediaUpload || !uploadPostImage) {
      throw new Error('Image upload is not enabled');
    }

    try {
      progress(0);
      
      // Convert blob to File
      const file = new File([blobInfo.blob()], blobInfo.filename(), {
        type: blobInfo.blob().type,
      });

      progress(50);
      
      const result = await uploadPostImage(file);
      progress(100);
      
      return result.url;
    } catch (error) {
      console.error('Image upload failed:', error);
      throw error;
    }
  }, [enableMediaUpload, uploadPostImage]);

  // TinyMCE configuration
  const editorConfig = useMemo(() => ({
    apiKey: import.meta.env.VITE_TINYMCE_API_KEY,
    height: autoHeight ? 'auto' : height,
    menubar: false,
    plugins: [
      'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
      'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
      'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
      'autosave', 'save', 'directionality', 'nonbreaking', 'pagebreak',
      'quickbars', 'codesample', 'accordion'
    ].filter(plugin => {
      // Conditionally include plugins based on props
      if (!enableTables && plugin === 'table') return false;
      if (!enableMediaUpload && (plugin === 'image' || plugin === 'media')) return false;
      if (!enableLinking && plugin === 'link') return false;
      if (!showWordCount && plugin === 'wordcount') return false;
      if (!enableAutoSave && plugin === 'autosave') return false;
      return true;
    }),
    toolbar: [
      'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough',
      'forecolor backcolor | align lineheight | numlist bullist indent outdent',
      enableLinking ? 'link unlink' : '',
      enableMediaUpload ? 'image media' : '',
      enableTables ? 'table' : '',
      'codesample | emoticons charmap | insertdatetime | preview code fullscreen help'
    ].filter(Boolean).join(' | '),
    quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
    quickbars_insert_toolbar: 'quickimage quicktable',
    contextmenu: 'link image table',
    skin: 'oxide',
    content_css: 'default',
    content_style: `
      body { 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; 
        font-size: 14px; 
        line-height: 1.6; 
        color: #374151;
      }
      .mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {
        color: #9CA3AF;
        font-style: italic;
      }
    `,
    placeholder,
    branding: false,
    promotion: false,
    resize: !autoHeight,
    autosave_ask_before_unload: enableAutoSave,
    autosave_interval: enableAutoSave ? `${autoSaveDelay / 1000}s` : undefined,
    autosave_prefix: 'tinymce-autosave-{path}{query}-{id}-',
    autosave_restore_when_empty: enableAutoSave,
    images_upload_handler: enableMediaUpload ? handleImageUpload : undefined,
    automatic_uploads: enableMediaUpload,
    file_picker_types: enableMediaUpload ? 'image' : undefined,
    paste_data_images: enableMediaUpload,
    image_advtab: true,
    image_caption: true,
    image_title: true,
    link_assume_external_targets: true,
    link_context_toolbar: true,
    table_use_colgroups: true,
    table_responsive_width: true,
    table_default_attributes: {
      class: 'table table-striped'
    },
    codesample_languages: [
      { text: 'HTML/XML', value: 'markup' },
      { text: 'JavaScript', value: 'javascript' },
      { text: 'TypeScript', value: 'typescript' },
      { text: 'CSS', value: 'css' },
      { text: 'Python', value: 'python' },
      { text: 'Java', value: 'java' },
      { text: 'C#', value: 'csharp' },
      { text: 'PHP', value: 'php' },
      { text: 'Ruby', value: 'ruby' },
      { text: 'Go', value: 'go' },
      { text: 'Rust', value: 'rust' },
      { text: 'SQL', value: 'sql' },
      { text: 'JSON', value: 'json' },
      { text: 'Bash', value: 'bash' }
    ],
    setup: (editor: any) => {
      editor.on('init', () => {
        setIsInitialized(true);
      });

      // Custom keyboard shortcuts
      if (enableKeyboardShortcuts) {
        editor.addShortcut('Meta+S', 'Save content', () => {
          if (onAutoSave) {
            onAutoSave(editor.getContent());
          }
        });
      }
    }
  }), [
    height, autoHeight, placeholder, enableTables, enableMediaUpload, enableLinking,
    showWordCount, enableAutoSave, autoSaveDelay, handleImageUpload, onAutoSave,
    enableKeyboardShortcuts
  ]);

  // Initialize statistics on mount
  useEffect(() => {
    if (value) {
      updateStats(value);
    }
  }, [value, updateStats]);

  return (
    <div className={`tinymce-editor-container ${className}`}>
      <Editor
        ref={editorRef}
        apiKey={import.meta.env.VITE_TINYMCE_API_KEY}
        value={value}
        onEditorChange={handleEditorChange}
        disabled={disabled}
        init={editorConfig}
      />
      
      {/* Statistics Display */}
      {(showWordCount || showDetailedStats) && isInitialized && (
        <div className="editor-stats mt-2 text-xs text-gray-500 dark:text-gray-400 flex gap-4">
          {showWordCount && (
            <>
              <span>{wordCount} words</span>
              <span>{characterCount} characters</span>
              <span>{readingTime} min read</span>
            </>
          )}
          {showDetailedStats && (
            <span>Editor ready</span>
          )}
        </div>
      )}
    </div>
  );
});

// Add display name for debugging
RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;
