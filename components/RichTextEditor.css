/* Rich Text Editor Styles */
.rich-text-editor {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  transition: border-color 0.2s ease;
}

.rich-text-editor:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Lexical Editor Container */
.lexical-editor-container {
  position: relative;
  background: white;
  border-radius: 8px;
}

/* Lexical Content Editable */
.lexical-content-editable {
  border: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  cursor: text;
  tab-size: 1;
}

.lexical-content-editable:focus {
  outline: none;
}

/* Placeholder */
.lexical-placeholder {
  color: #9ca3af;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 16px;
  left: 16px;
  font-size: 14px;
  user-select: none;
  display: inline-block;
  pointer-events: none;
}

/* Lexical Toolbar */
.lexical-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  flex-wrap: wrap;
}

.lexical-toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.lexical-toolbar-button:hover {
  background-color: #e2e8f0;
}

.lexical-toolbar-button:active {
  background-color: #d1d5db;
}

.lexical-toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: #d1d5db;
  margin: 0 4px;
}

/* Lexical Text Styles */
.lexical-text-bold {
  font-weight: bold;
}

.lexical-text-italic {
  font-style: italic;
}

.lexical-text-underline {
  text-decoration: underline;
}

.lexical-text-strikethrough {
  text-decoration: line-through;
}

.lexical-text-code {
  background-color: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

/* Lexical Heading Styles */
.lexical-heading-h1 {
  font-size: 2em;
  font-weight: 700;
  margin: 1em 0 0.5em 0;
  line-height: 1.2;
}

.lexical-heading-h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  line-height: 1.3;
}

.lexical-heading-h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  line-height: 1.4;
}

.lexical-heading-h4,
.lexical-heading-h5,
.lexical-heading-h6 {
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  line-height: 1.4;
}

/* Lexical Quote Styles */
.lexical-quote {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 16px 0;
  color: #6b7280;
  font-style: italic;
}

/* Lexical Code Styles */
.lexical-code {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Lexical List Styles */
.lexical-list-ol,
.lexical-list-ul {
  margin: 16px 0;
  padding-left: 24px;
}

.lexical-listitem {
  margin: 4px 0;
}

.lexical-nested-listitem {
  list-style-type: none;
}

/* Lexical Link Styles */
.lexical-link {
  color: #3b82f6;
  text-decoration: underline;
}

.lexical-link:hover {
  color: #1d4ed8;
}

/* Lexical Paragraph */
.lexical-paragraph {
  margin: 0 0 16px 0;
}

/* Enhanced Image Styles */
.lexical-image-container {
  position: relative;
  display: block;
  margin: 16px 0;
  text-align: center;
}

.lexical-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.lexical-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Enhanced Table Styles */
.lexical-table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.lexical-table-cell {
  border: 1px solid #e2e8f0;
  padding: 12px;
  background: white;
  transition: background-color 0.2s ease;
}

.lexical-table-cell:hover {
  background: #f8fafc;
}

.lexical-table-cell-header {
  background: #f1f5f9;
  font-weight: 600;
  text-align: left;
}

/* Enhanced Toolbar Styles */
.enhanced-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  flex-wrap: wrap;
  min-height: 48px;
}

.enhanced-toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 0 8px;
}

.enhanced-toolbar-button:hover {
  background-color: #e2e8f0;
  transform: translateY(-1px);
}

.enhanced-toolbar-button:active {
  background-color: #d1d5db;
  transform: translateY(0);
}

.enhanced-toolbar-button.active {
  background-color: #dbeafe;
  color: #1d4ed8;
  box-shadow: 0 0 0 1px #3b82f6;
}

.enhanced-toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: #d1d5db;
  margin: 0 4px;
}

/* Media Gallery Styles */
.media-gallery-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  animation: fadeIn 0.2s ease;
}

.media-gallery-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 64rem;
  height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
}

.media-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  padding: 16px;
}

.media-gallery-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.media-gallery-item:hover {
  transform: scale(1.05);
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Link Editor Styles */
.link-editor-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  animation: fadeIn 0.2s ease;
}

.link-editor-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 32rem;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.link-preview {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.link-preview:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Loading States */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Word Count and Stats */
.editor-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 16px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  font-size: 12px;
  color: #6b7280;
}

.editor-stats-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.auto-save-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.auto-save-indicator.saving {
  background: #fef3c7;
  color: #92400e;
}

.auto-save-indicator.saved {
  background: #d1fae5;
  color: #065f46;
}

.auto-save-indicator.unsaved {
  background: #fee2e2;
  color: #991b1b;
}</anical:parameter>
<parameter name="old_str_start_line_number">106

/* Disabled State */
.rich-text-editor .ql-container.ql-disabled .ql-editor {
  background-color: #f9fafb;
  color: #6b7280;
}

.rich-text-editor .ql-toolbar.ql-disabled {
  background-color: #f3f4f6;
}

.rich-text-editor .ql-toolbar.ql-disabled button,
.rich-text-editor .ql-toolbar.ql-disabled .ql-picker {
  opacity: 0.5;
  pointer-events: none;
}

/* Focus States */
.rich-text-editor .ql-editor:focus {
  outline: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .rich-text-editor .ql-toolbar {
    padding: 6px 8px;
  }
  
  .rich-text-editor .ql-toolbar .ql-formats {
    margin-right: 8px;
  }
  
  .rich-text-editor .ql-editor {
    padding: 12px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .rich-text-editor {
    border-color: #374151;
    background: #1f2937;
  }

  .lexical-editor-container {
    background: #1f2937;
  }

  .lexical-content-editable {
    color: #f9fafb;
  }

  .lexical-placeholder {
    color: #6b7280;
  }

  /* Enhanced Toolbar Dark Mode */
  .enhanced-toolbar {
    background: #111827;
    border-bottom-color: #374151;
  }

  .enhanced-toolbar-button {
    color: #d1d5db;
  }

  .enhanced-toolbar-button:hover {
    background-color: #374151;
  }

  .enhanced-toolbar-button:active {
    background-color: #4b5563;
  }

  .enhanced-toolbar-button.active {
    background-color: #1e3a8a;
    color: #93c5fd;
  }

  .enhanced-toolbar-divider {
    background-color: #4b5563;
  }

  /* Table Dark Mode */
  .lexical-table {
    border-color: #374151;
  }

  .lexical-table-cell {
    border-color: #374151;
    background: #1f2937;
    color: #f9fafb;
  }

  .lexical-table-cell:hover {
    background: #374151;
  }

  .lexical-table-cell-header {
    background: #374151;
    color: #f9fafb;
  }

  /* Media Gallery Dark Mode */
  .media-gallery-modal {
    background: #1f2937;
    color: #f9fafb;
  }

  .media-gallery-item {
    background: #374151;
  }

  /* Link Editor Dark Mode */
  .link-editor-modal {
    background: #1f2937;
    color: #f9fafb;
  }

  .link-preview {
    border-color: #374151;
    background: #374151;
    color: #f9fafb;
  }

  .link-preview:hover {
    border-color: #3b82f6;
    background: #1e3a8a;
  }

  /* Stats Dark Mode */
  .editor-stats {
    background: #111827;
    border-top-color: #374151;
    color: #9ca3af;
  }

  .auto-save-indicator.saving {
    background: #451a03;
    color: #fbbf24;
  }

  .auto-save-indicator.saved {
    background: #064e3b;
    color: #34d399;
  }

  .auto-save-indicator.unsaved {
    background: #7f1d1d;
    color: #f87171;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-toolbar {
    padding: 6px 8px;
    gap: 2px;
  }

  .enhanced-toolbar-button {
    min-width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .media-gallery-modal {
    margin: 16px;
    height: calc(100vh - 32px);
  }

  .media-gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    padding: 12px;
  }

  .link-editor-modal {
    margin: 16px;
    max-height: calc(100vh - 32px);
  }
}

/* Focus States */
.enhanced-toolbar-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.media-gallery-item:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .enhanced-toolbar-button,
  .media-gallery-item,
  .lexical-image,
  .link-preview {
    transition: none;
  }

  .media-gallery-overlay,
  .link-editor-overlay {
    animation: none;
  }

  .media-gallery-modal,
  .link-editor-modal {
    animation: none;
  }
}
