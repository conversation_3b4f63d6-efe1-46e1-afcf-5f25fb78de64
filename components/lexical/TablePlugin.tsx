import React, { useCallback } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { TablePlugin as LexicalTablePlugin } from '@lexical/react/LexicalTablePlugin';
import { 
  $createTableNodeWithDimensions,
  INSERT_TABLE_COMMAND,
  TableNode,
  TableCellNode,
  TableRowNode,
  $isTableNode,
  $isTableCellNode,
  $isTableRowNode
} from '@lexical/table';
import { 
  $getSelection, 
  $isRangeSelection, 
  $insertNodes,
  $getNodeByKey,
  COMMAND_PRIORITY_EDITOR
} from 'lexical';

export interface TablePluginProps {
  cellEditorConfig?: {
    namespace: string;
    nodes?: any[];
    onError?: (error: Error) => void;
    theme?: any;
  };
}

export function EnhancedTablePlugin({ cellEditorConfig }: TablePluginProps) {
  const [editor] = useLexicalComposerContext();

  const insertTable = useCallback((rows: number, columns: number) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const tableNode = $createTableNodeWithDimensions(rows, columns, false);
        $insertNodes([tableNode]);
      }
    });
  }, [editor]);

  // Register table insertion command
  React.useEffect(() => {
    const removeCommand = editor.registerCommand(
      INSERT_TABLE_COMMAND,
      (payload: { rows: number; columns: number }) => {
        insertTable(payload.rows, payload.columns);
        return true;
      },
      COMMAND_PRIORITY_EDITOR
    );

    return removeCommand;
  }, [editor, insertTable]);

  // Register custom table commands
  React.useEffect(() => {
    const removeInsertRowCommand = editor.registerCommand(
      'INSERT_TABLE_ROW',
      (payload: { insertAfter?: boolean } = {}) => {
        editor.update(() => {
          const selection = $getSelection();
          if ($isRangeSelection(selection)) {
            const anchorNode = selection.anchor.getNode();
            const tableCell = anchorNode.getParent();
            
            if ($isTableCellNode(tableCell)) {
              const tableRow = tableCell.getParent();
              if ($isTableRowNode(tableRow)) {
                const table = tableRow.getParent();
                if ($isTableNode(table)) {
                  const newRow = tableRow.clone();
                  // Clear content from cloned cells
                  newRow.getChildren().forEach(cell => {
                    if ($isTableCellNode(cell)) {
                      cell.clear();
                    }
                  });
                  
                  if (payload.insertAfter) {
                    tableRow.insertAfter(newRow);
                  } else {
                    tableRow.insertBefore(newRow);
                  }
                }
              }
            }
          }
        });
        return true;
      },
      COMMAND_PRIORITY_EDITOR
    );

    const removeInsertColumnCommand = editor.registerCommand(
      'INSERT_TABLE_COLUMN',
      (payload: { insertAfter?: boolean } = {}) => {
        editor.update(() => {
          const selection = $getSelection();
          if ($isRangeSelection(selection)) {
            const anchorNode = selection.anchor.getNode();
            const tableCell = anchorNode.getParent();
            
            if ($isTableCellNode(tableCell)) {
              const tableRow = tableCell.getParent();
              if ($isTableRowNode(tableRow)) {
                const table = tableRow.getParent();
                if ($isTableNode(table)) {
                  const cellIndex = tableRow.getChildren().indexOf(tableCell);
                  
                  // Add new cell to each row
                  table.getChildren().forEach(row => {
                    if ($isTableRowNode(row)) {
                      const newCell = new TableCellNode();
                      const cells = row.getChildren();
                      
                      if (payload.insertAfter && cellIndex < cells.length - 1) {
                        cells[cellIndex].insertAfter(newCell);
                      } else if (!payload.insertAfter && cellIndex > 0) {
                        cells[cellIndex].insertBefore(newCell);
                      } else {
                        row.append(newCell);
                      }
                    }
                  });
                }
              }
            }
          }
        });
        return true;
      },
      COMMAND_PRIORITY_EDITOR
    );

    const removeDeleteRowCommand = editor.registerCommand(
      'DELETE_TABLE_ROW',
      () => {
        editor.update(() => {
          const selection = $getSelection();
          if ($isRangeSelection(selection)) {
            const anchorNode = selection.anchor.getNode();
            const tableCell = anchorNode.getParent();
            
            if ($isTableCellNode(tableCell)) {
              const tableRow = tableCell.getParent();
              if ($isTableRowNode(tableRow)) {
                const table = tableRow.getParent();
                if ($isTableNode(table) && table.getChildren().length > 1) {
                  tableRow.remove();
                }
              }
            }
          }
        });
        return true;
      },
      COMMAND_PRIORITY_EDITOR
    );

    const removeDeleteColumnCommand = editor.registerCommand(
      'DELETE_TABLE_COLUMN',
      () => {
        editor.update(() => {
          const selection = $getSelection();
          if ($isRangeSelection(selection)) {
            const anchorNode = selection.anchor.getNode();
            const tableCell = anchorNode.getParent();
            
            if ($isTableCellNode(tableCell)) {
              const tableRow = tableCell.getParent();
              if ($isTableRowNode(tableRow)) {
                const table = tableRow.getParent();
                if ($isTableNode(table)) {
                  const cellIndex = tableRow.getChildren().indexOf(tableCell);
                  
                  // Check if table has more than one column
                  if (tableRow.getChildren().length > 1) {
                    // Remove cell from each row at the same index
                    table.getChildren().forEach(row => {
                      if ($isTableRowNode(row)) {
                        const cells = row.getChildren();
                        if (cells[cellIndex]) {
                          cells[cellIndex].remove();
                        }
                      }
                    });
                  }
                }
              }
            }
          }
        });
        return true;
      },
      COMMAND_PRIORITY_EDITOR
    );

    return () => {
      removeInsertRowCommand();
      removeInsertColumnCommand();
      removeDeleteRowCommand();
      removeDeleteColumnCommand();
    };
  }, [editor]);

  return <LexicalTablePlugin hasCellMerge={true} hasCellBackgroundColor={true} />;
}

// Table creation modal component
export interface TableCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTable: (rows: number, columns: number) => void;
}

export const TableCreationModal: React.FC<TableCreationModalProps> = ({
  isOpen,
  onClose,
  onCreateTable
}) => {
  const [rows, setRows] = React.useState(3);
  const [columns, setColumns] = React.useState(3);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onCreateTable(rows, columns);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-dark-bg rounded-lg shadow-xl p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Create Table
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Rows
            </label>
            <input
              type="number"
              min="1"
              max="20"
              value={rows}
              onChange={(e) => setRows(parseInt(e.target.value) || 1)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Columns
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={columns}
              onChange={(e) => setColumns(parseInt(e.target.value) || 1)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
          
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Table
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
