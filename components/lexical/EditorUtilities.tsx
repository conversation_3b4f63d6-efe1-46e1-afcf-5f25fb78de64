import React, { useState, useEffect, useCallback } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getRoot } from 'lexical';
import { $generateHtmlFromNodes } from '@lexical/html';

// Word Count and Reading Time Plugin
export function WordCountPlugin() {
  const [editor] = useLexicalComposerContext();
  const [wordCount, setWordCount] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);

  const calculateStats = useCallback(() => {
    editor.getEditorState().read(() => {
      const root = $getRoot();
      const textContent = root.getTextContent();
      
      // Calculate word count
      const words = textContent.trim().split(/\s+/).filter(word => word.length > 0);
      const wordCount = words.length;
      
      // Calculate character count (excluding spaces)
      const characterCount = textContent.replace(/\s/g, '').length;
      
      // Calculate reading time (average 200 words per minute)
      const readingTime = Math.ceil(wordCount / 200);
      
      setWordCount(wordCount);
      setCharacterCount(characterCount);
      setReadingTime(readingTime);
    });
  }, [editor]);

  useEffect(() => {
    // Calculate stats on initial load
    calculateStats();

    // Register update listener
    const removeUpdateListener = editor.registerUpdateListener(() => {
      calculateStats();
    });

    return removeUpdateListener;
  }, [editor, calculateStats]);

  return (
    <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400 px-3 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
      <span>Words: {wordCount}</span>
      <span>Characters: {characterCount}</span>
      <span>Reading time: {readingTime} min</span>
    </div>
  );
}

// Auto-save Plugin
export interface AutoSavePluginProps {
  onSave: (content: string) => Promise<void>;
  delay?: number; // Auto-save delay in milliseconds
  enabled?: boolean;
}

export function AutoSavePlugin({ onSave, delay = 30000, enabled = true }: AutoSavePluginProps) {
  const [editor] = useLexicalComposerContext();
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const saveContent = useCallback(async () => {
    if (!enabled || isSaving) return;

    setIsSaving(true);
    try {
      const htmlString = editor.getEditorState().read(() => {
        return $generateHtmlFromNodes(editor, null);
      });
      
      await onSave(htmlString);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsSaving(false);
    }
  }, [editor, onSave, enabled, isSaving]);

  useEffect(() => {
    if (!enabled) return;

    let timeoutId: NodeJS.Timeout;

    const removeUpdateListener = editor.registerUpdateListener(({ dirtyElements, dirtyLeaves }) => {
      if (dirtyElements.size > 0 || dirtyLeaves.size > 0) {
        setHasUnsavedChanges(true);
        
        // Clear existing timeout
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        // Set new timeout for auto-save
        timeoutId = setTimeout(saveContent, delay);
      }
    });

    return () => {
      removeUpdateListener();
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [editor, saveContent, delay, enabled]);

  // Manual save on Ctrl+S
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        saveContent();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [saveContent]);

  if (!enabled) return null;

  return (
    <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 px-3 py-1">
      {isSaving && (
        <span className="flex items-center gap-1">
          <div className="w-3 h-3 border border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
          Saving...
        </span>
      )}
      {!isSaving && hasUnsavedChanges && (
        <span className="text-orange-500">Unsaved changes</span>
      )}
      {!isSaving && !hasUnsavedChanges && lastSaved && (
        <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
      )}
    </div>
  );
}

// Keyboard Shortcuts Plugin
export function KeyboardShortcutsPlugin() {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const { ctrlKey, metaKey, shiftKey, key } = event;
      const isModifierPressed = ctrlKey || metaKey;

      if (!isModifierPressed) return;

      switch (key.toLowerCase()) {
        case 'b':
          event.preventDefault();
          editor.dispatchCommand('FORMAT_TEXT_COMMAND', 'bold');
          break;
        case 'i':
          event.preventDefault();
          editor.dispatchCommand('FORMAT_TEXT_COMMAND', 'italic');
          break;
        case 'u':
          event.preventDefault();
          editor.dispatchCommand('FORMAT_TEXT_COMMAND', 'underline');
          break;
        case 'k':
          event.preventDefault();
          // Trigger link insertion
          editor.dispatchCommand('SHOW_LINK_EDITOR', undefined);
          break;
        case 'e':
          if (shiftKey) {
            event.preventDefault();
            editor.dispatchCommand('FORMAT_ELEMENT_COMMAND', 'center');
          }
          break;
        case 'l':
          if (shiftKey) {
            event.preventDefault();
            editor.dispatchCommand('FORMAT_ELEMENT_COMMAND', 'left');
          }
          break;
        case 'r':
          if (shiftKey) {
            event.preventDefault();
            editor.dispatchCommand('FORMAT_ELEMENT_COMMAND', 'right');
          }
          break;
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
        case '6':
          if (shiftKey) {
            event.preventDefault();
            editor.dispatchCommand('INSERT_HEADING', `h${key}`);
          }
          break;
        case 'enter':
          if (shiftKey) {
            event.preventDefault();
            editor.dispatchCommand('INSERT_LINE_BREAK', undefined);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [editor]);

  return null;
}

// Content Statistics Display
export interface ContentStatsProps {
  className?: string;
}

export function ContentStats({ className = '' }: ContentStatsProps) {
  const [editor] = useLexicalComposerContext();
  const [stats, setStats] = useState({
    words: 0,
    characters: 0,
    charactersNoSpaces: 0,
    paragraphs: 0,
    sentences: 0,
    readingTime: 0,
    speakingTime: 0
  });

  const calculateDetailedStats = useCallback(() => {
    editor.getEditorState().read(() => {
      const root = $getRoot();
      const textContent = root.getTextContent();
      
      // Basic counts
      const words = textContent.trim().split(/\s+/).filter(word => word.length > 0);
      const wordCount = words.length;
      const characterCount = textContent.length;
      const charactersNoSpaces = textContent.replace(/\s/g, '').length;
      
      // Paragraph count (split by double line breaks or paragraph elements)
      const paragraphs = textContent.split(/\n\s*\n/).filter(p => p.trim().length > 0);
      const paragraphCount = Math.max(paragraphs.length, 1);
      
      // Sentence count (approximate)
      const sentences = textContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
      const sentenceCount = sentences.length;
      
      // Reading time (200 words per minute)
      const readingTime = Math.ceil(wordCount / 200);
      
      // Speaking time (150 words per minute)
      const speakingTime = Math.ceil(wordCount / 150);
      
      setStats({
        words: wordCount,
        characters: characterCount,
        charactersNoSpaces,
        paragraphs: paragraphCount,
        sentences: sentenceCount,
        readingTime,
        speakingTime
      });
    });
  }, [editor]);

  useEffect(() => {
    calculateDetailedStats();

    const removeUpdateListener = editor.registerUpdateListener(() => {
      calculateDetailedStats();
    });

    return removeUpdateListener;
  }, [editor, calculateDetailedStats]);

  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg ${className}`}>
      <div className="text-center">
        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.words}</div>
        <div className="text-sm text-gray-600 dark:text-gray-400">Words</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.characters}</div>
        <div className="text-sm text-gray-600 dark:text-gray-400">Characters</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.paragraphs}</div>
        <div className="text-sm text-gray-600 dark:text-gray-400">Paragraphs</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{stats.readingTime}</div>
        <div className="text-sm text-gray-600 dark:text-gray-400">Min read</div>
      </div>
    </div>
  );
}

// Export all utilities as a combined component
export interface EditorUtilitiesProps {
  onAutoSave?: (content: string) => Promise<void>;
  autoSaveDelay?: number;
  autoSaveEnabled?: boolean;
  showWordCount?: boolean;
  showDetailedStats?: boolean;
  enableKeyboardShortcuts?: boolean;
}

export function EditorUtilities({
  onAutoSave,
  autoSaveDelay = 30000,
  autoSaveEnabled = true,
  showWordCount = true,
  showDetailedStats = false,
  enableKeyboardShortcuts = true
}: EditorUtilitiesProps) {
  return (
    <>
      {enableKeyboardShortcuts && <KeyboardShortcutsPlugin />}
      {onAutoSave && (
        <AutoSavePlugin
          onSave={onAutoSave}
          delay={autoSaveDelay}
          enabled={autoSaveEnabled}
        />
      )}
      {showWordCount && <WordCountPlugin />}
      {showDetailedStats && <ContentStats />}
    </>
  );
}
