import React, { useCallback } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $createParagraphNode,
  $insertNodes,
  $isRootOrShadowRoot,
  $getSelection,
  $isRangeSelection,
  DecoratorNode,
  NodeKey,
  LexicalNode,
  SerializedLexicalNode,
  Spread
} from 'lexical';

export interface ImagePluginProps {
  onImageUpload?: (file: File) => Promise<{ url: string; alt?: string }>;
}

export function ImagePlugin({ onImageUpload }: ImagePluginProps) {
  const [editor] = useLexicalComposerContext();

  const insertImage = useCallback((url: string, alt?: string, width?: number, height?: number) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const imageNode = $createImageNode({
          src: url,
          alt: alt || '',
          width,
          height,
        });
        
        if ($isRootOrShadowRoot(selection.anchor.getNode())) {
          const paragraphNode = $createParagraphNode();
          paragraphNode.append(imageNode);
          $insertNodes([paragraphNode]);
        } else {
          $insertNodes([imageNode]);
        }
      }
    });
  }, [editor]);

  const handleImageUpload = useCallback(async (file: File) => {
    if (!onImageUpload) return;
    
    try {
      const result = await onImageUpload(file);
      insertImage(result.url, result.alt);
    } catch (error) {
      console.error('Image upload failed:', error);
    }
  }, [onImageUpload, insertImage]);

  const handleDrop = useCallback((event: DragEvent) => {
    const files = event.dataTransfer?.files;
    if (!files || files.length === 0) return false;

    const file = files[0];
    if (!file.type.startsWith('image/')) return false;

    event.preventDefault();
    handleImageUpload(file);
    return true;
  }, [handleImageUpload]);

  const handlePaste = useCallback((event: ClipboardEvent) => {
    const files = event.clipboardData?.files;
    if (!files || files.length === 0) return false;

    const file = files[0];
    if (!file.type.startsWith('image/')) return false;

    event.preventDefault();
    handleImageUpload(file);
    return true;
  }, [handleImageUpload]);

  React.useEffect(() => {
    const removeDropListener = editor.registerRootListener((rootElement, prevRootElement) => {
      if (prevRootElement !== null) {
        prevRootElement.removeEventListener('drop', handleDrop);
        prevRootElement.removeEventListener('paste', handlePaste);
      }
      if (rootElement !== null) {
        rootElement.addEventListener('drop', handleDrop);
        rootElement.addEventListener('paste', handlePaste);
      }
    });

    return removeDropListener;
  }, [editor, handleDrop, handlePaste]);

  // Expose insertImage function for external use
  React.useEffect(() => {
    const insertImageCommand = editor.registerCommand(
      'INSERT_IMAGE',
      (payload: { url: string; alt?: string; width?: number; height?: number }) => {
        insertImage(payload.url, payload.alt, payload.width, payload.height);
        return true;
      },
      0
    );

    return insertImageCommand;
  }, [editor, insertImage]);

  return null;
}

// Serialized Image Node type
export type SerializedImageNode = Spread<
  {
    src: string;
    alt: string;
    width?: number;
    height?: number;
  },
  SerializedLexicalNode
>;

// Custom Image Node for Lexical
export class ImageNode extends DecoratorNode<React.ReactElement> {
  __src: string;
  __alt: string;
  __width?: number;
  __height?: number;

  static getType(): string {
    return 'image';
  }

  static clone(node: ImageNode): ImageNode {
    return new ImageNode(
      node.__src,
      node.__alt,
      node.__width,
      node.__height,
      node.__key
    );
  }

  constructor(
    src: string,
    alt?: string,
    width?: number,
    height?: number,
    key?: NodeKey
  ) {
    super(key);
    this.__src = src;
    this.__alt = alt || '';
    this.__width = width;
    this.__height = height;
  }

  createDOM(): HTMLElement {
    const div = document.createElement('div');
    div.className = 'lexical-image-container';
    div.style.textAlign = 'center';
    div.style.margin = '16px 0';
    return div;
  }

  updateDOM(): boolean {
    return false;
  }

  decorate(): React.ReactElement {
    return (
      <img
        src={this.__src}
        alt={this.__alt}
        style={{
          maxWidth: '100%',
          height: this.__height ? `${this.__height}px` : 'auto',
          display: 'block',
          margin: '0 auto',
          borderRadius: '8px',
          width: this.__width ? `${this.__width}px` : undefined,
        }}
        draggable={false}
        className="lexical-image"
      />
    );
  }

  static importJSON(serializedNode: SerializedImageNode): ImageNode {
    const { src, alt, width, height } = serializedNode;
    return $createImageNode({ src, alt, width, height });
  }

  exportJSON(): SerializedImageNode {
    return {
      src: this.__src,
      alt: this.__alt,
      width: this.__width,
      height: this.__height,
      type: 'image',
      version: 1,
    };
  }

  getSrc(): string {
    return this.__src;
  }

  getAlt(): string {
    return this.__alt;
  }

  setAlt(alt: string): void {
    const writable = this.getWritable();
    writable.__alt = alt;
  }

  setSrc(src: string): void {
    const writable = this.getWritable();
    writable.__src = src;
  }

  getWidth(): number | undefined {
    return this.__width;
  }

  getHeight(): number | undefined {
    return this.__height;
  }

  setWidth(width: number): void {
    const writable = this.getWritable();
    writable.__width = width;
  }

  setHeight(height: number): void {
    const writable = this.getWritable();
    writable.__height = height;
  }

  isInline(): false {
    return false;
  }
}

export function $createImageNode({
  src,
  alt,
  width,
  height,
}: {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
}): ImageNode {
  return new ImageNode(src, alt, width, height);
}

export function $isImageNode(node: any): node is ImageNode {
  return node instanceof ImageNode;
}
